# 📦 WhatsApp Chat <PERSON>er + <PERSON><PERSON>suntore (Unificato)
# Requisiti: pip install selenium openai
# Chrome + chromedriver richiesto

import time, json, re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import openai

# === CONFIG ===
OPENAI_API_KEY = "sk-..."  # Inserisci la tua API key qui
GPT_MODEL = "gpt-4"
MAX_CHATS = 5  # Quante chat leggere
SCROLL_PAUSE = 1.5

openai.api_key = OPENAI_API_KEY

def setup_driver():
    options = Options()
    options.add_argument("--user-data-dir=./profile")  # Persistenza login
    driver = webdriver.Chrome(options=options)
    driver.get("https://web.whatsapp.com")
    print("[!] Scansiona il codice QR se richiesto...")
    input("Premi INVIO quando WhatsApp Web è caricato e visibile...")
    return driver

def get_chats(driver):
    print("[+] Caricamento chat...")
    time.sleep(2)
    chats = driver.find_elements(By.CSS_SELECTOR, "div[role='row']")
    return chats[:MAX_CHATS]

def open_chat(driver, chat_element):
    chat_element.click()
    time.sleep(2)

def scroll_chat_up(driver, scrolls=10):
    for _ in range(scrolls):
        driver.execute_script("document.querySelector('div.copyable-area').scrollTop = 0")
        time.sleep(SCROLL_PAUSE)

def extract_messages(driver):
    elems = driver.find_elements(By.CSS_SELECTOR, "div.message-in, div.message-out")
    today = datetime.now().date()
    messages = []
    for el in elems:
        try:
            msg_text = el.find_element(By.CSS_SELECTOR, "span.selectable-text").text
            meta = el.find_element(By.CSS_SELECTOR, "span[aria-label]").get_attribute("aria-label")
            match = re.search(r'(\d{1,2}:\d{2})', meta)
            sender = "Tu" if "message-out" in el.get_attribute("class") else "Altro"
            if match:
                dt = datetime.now().replace(hour=int(match[1].split(":")[0]), minute=int(match[1].split(":")[1]))
                if dt.date() == today:
                    messages.append({"sender": sender, "text": msg_text})
        except:
            continue
    return messages

def summarize_chat(chat_name, messages):
    if not messages: return "Nessun messaggio per oggi."
    conv = "\n".join(f"{m['sender']}: {m['text']}" for m in messages)
    prompt = (
        f"Questa è una chat WhatsApp chiamata '{chat_name}' di oggi."
        f" Riassumila in max 8 punti. Includi eventuali To-do o Eventi.\n\n{conv}\n\n"
        "- Riassunto:\n- To-do:\n- Eventi:\n"
    )
    resp = openai.ChatCompletion.create(
        model=GPT_MODEL,
        messages=[{"role": "user", "content": prompt}]
    )
    return resp.choices[0].message.content

# === MAIN ===
driver = setup_driver()
all_chats = get_chats(driver)

for idx, chat in enumerate(all_chats):
    print(f"[+] Elaborazione chat {idx+1}/{len(all_chats)}")
    open_chat(driver, chat)
    scroll_chat_up(driver, scrolls=5)
    name = chat.text.split("\n")[0]  # Nome visibile della chat
    messages = extract_messages(driver)
    summary = summarize_chat(name, messages)
    print(f"===== {name} =====")
    print(summary)
    print("\n" + "="*40 + "\n")

driver.quit()
