# 📦 WhatsApp Chat Scraper + <PERSON><PERSON><PERSON><PERSON> (Unificato)
# Requisiti: pip install selenium openai
# Chrome + chromedriver richiesto

import time, json, re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import openai

# === CONFIG ===
OPENAI_API_KEY = "********************************************************************************************************************************************************************"  # Inserisci la tua API key qui
GPT_MODEL = "gpt-4o-2024-08-06"
MAX_CHATS = 5  # Quante chat leggere
SCROLL_PAUSE = 1.5

openai.api_key = OPENAI_API_KEY

def setup_driver():
    print("[+] Avvio Chrome...")

    try:
        # Configurazione minima per Chrome
        options = Options()

        # Usa webdriver-manager per scaricare automaticamente ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        driver.get("https://web.whatsapp.com")
        print("[!] Scansiona il codice QR se richiesto...")
        input("Premi INVIO quando WhatsApp Web è caricato e visibile...")
        return driver
    except Exception as e:
        print(f"[!] Errore nell'avvio di Chrome: {e}")
        print("[!] Assicurati che Chrome sia installato e aggiornato.")
        raise

def get_chats(driver):
    print("[+] Caricamento chat...")
    time.sleep(5)  # Aspetta di più per il caricamento

    # Prova diversi selettori per le chat
    selectors = [
        "div[role='row']",
        "div[data-testid='cell-frame-container']",
        "div[data-testid='chat-list-item']",
        "div._ak8q",  # Selettore alternativo
        "div[role='listitem']"
    ]

    chats = []
    for selector in selectors:
        print(f"[DEBUG] Provo selettore: {selector}")
        chats = driver.find_elements(By.CSS_SELECTOR, selector)
        print(f"[DEBUG] Trovate {len(chats)} chat con selettore {selector}")
        if chats:
            break

    if not chats:
        print("[!] ERRORE: Nessuna chat trovata!")
        print("[DEBUG] Provo a fare screenshot per debug...")
        driver.save_screenshot("debug_screenshot.png")
        print("[DEBUG] Screenshot salvato come debug_screenshot.png")

        # Prova a stampare il HTML della pagina per debug
        print("[DEBUG] Titolo pagina:", driver.title)
        print("[DEBUG] URL corrente:", driver.current_url)

    return chats[:MAX_CHATS]

def open_chat(driver, chat_element):
    chat_element.click()
    time.sleep(2)

def scroll_chat_up(driver, scrolls=10):
    for _ in range(scrolls):
        driver.execute_script("document.querySelector('div.copyable-area').scrollTop = 0")
        time.sleep(SCROLL_PAUSE)

def extract_messages(driver):
    elems = driver.find_elements(By.CSS_SELECTOR, "div.message-in, div.message-out")
    today = datetime.now().date()
    messages = []
    for el in elems:
        try:
            msg_text = el.find_element(By.CSS_SELECTOR, "span.selectable-text").text
            meta = el.find_element(By.CSS_SELECTOR, "span[aria-label]").get_attribute("aria-label")
            match = re.search(r'(\d{1,2}:\d{2})', meta)
            sender = "Tu" if "message-out" in el.get_attribute("class") else "Altro"
            if match:
                dt = datetime.now().replace(hour=int(match[1].split(":")[0]), minute=int(match[1].split(":")[1]))
                if dt.date() == today:
                    messages.append({"sender": sender, "text": msg_text})
        except:
            continue
    return messages

def summarize_chat(chat_name, messages):
    if not messages: return "Nessun messaggio per oggi."
    conv = "\n".join(f"{m['sender']}: {m['text']}" for m in messages)
    prompt = (
        f"Questa è una chat WhatsApp chiamata '{chat_name}' di oggi."
        f" Riassumila in max 8 punti. Includi eventuali To-do o Eventi.\n\n{conv}\n\n"
        "- Riassunto:\n- To-do:\n- Eventi:\n"
    )
    resp = openai.ChatCompletion.create(
        model=GPT_MODEL,
        messages=[{"role": "user", "content": prompt}]
    )
    return resp.choices[0].message.content

# === MAIN ===
driver = setup_driver()
all_chats = get_chats(driver)

for idx, chat in enumerate(all_chats):
    print(f"[+] Elaborazione chat {idx+1}/{len(all_chats)}")
    open_chat(driver, chat)
    scroll_chat_up(driver, scrolls=5)
    name = chat.text.split("\n")[0]  # Nome visibile della chat
    messages = extract_messages(driver)
    summary = summarize_chat(name, messages)
    print(f"===== {name} =====")
    print(summary)
    print("\n" + "="*40 + "\n")

driver.quit()
